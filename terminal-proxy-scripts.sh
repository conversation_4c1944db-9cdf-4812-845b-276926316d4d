# #!/bin/bash
# # nordvpn-terminal-proxy.sh
# # 只在當前終端會話中設置代理，不影響其他程序

# # 配置你的 NordVPN SOCKS5 憑證
# NORDVPN_USER="2qfCDkqLEw8P3kf4ZAdm5zMu"
# NORDVPN_PASS="7R4LttY9hDK8rHjSmvBPM7q7"

# # 選擇最近的 SOCKS5 服務器（亞洲用戶建議使用新加坡或日本附近的服務器）
# # 由於沒有台灣的 SOCKS5，建議使用美西服務器以獲得較好的延遲
# PROXY_SERVER="los-angeles.us.socks.nordhold.net"
# PROXY_PORT="1080"

# # 顏色定義
# GREEN='\033[0;32m'
# RED='\033[0;31m'
# YELLOW='\033[1;33m'
# NC='\033[0m' # No Color

# # 啟用代理函數
# proxy_on() {
#     # 設置環境變量（只影響當前終端會話）
#     export ALL_PROXY="socks5://${NORDVPN_USER}:${NORDVPN_PASS}@${PROXY_SERVER}:${PROXY_PORT}"
#     export HTTP_PROXY="$ALL_PROXY"
#     export HTTPS_PROXY="$ALL_PROXY"
#     export http_proxy="$ALL_PROXY"
#     export https_proxy="$ALL_PROXY"
#     export NO_PROXY="localhost,127.0.0.1,*.local,10.0.0.0/8,**********/12,***********/16"
#     export no_proxy="$NO_PROXY"
    
#     echo -e "${GREEN}✓ 代理已在當前終端啟用${NC}"
#     echo -e "${YELLOW}代理服務器: ${PROXY_SERVER}:${PROXY_PORT}${NC}"
    
#     # 測試連接
#     test_proxy
# }

# # 禁用代理函數
# proxy_off() {
#     unset ALL_PROXY HTTP_PROXY HTTPS_PROXY http_proxy https_proxy NO_PROXY no_proxy
#     echo -e "${GREEN}✓ 代理已在當前終端禁用${NC}"
# }

# # 檢查代理狀態
# proxy_status() {
#     if [ -n "$ALL_PROXY" ]; then
#         echo -e "${GREEN}代理狀態: 已啟用${NC}"
#         echo -e "當前代理: $ALL_PROXY"
#         echo -e "排除列表: $NO_PROXY"
#     else
#         echo -e "${RED}代理狀態: 未啟用${NC}"
#     fi
# }

# # 測試代理連接
# test_proxy() {
#     echo -e "\n${YELLOW}正在測試代理連接...${NC}"
    
#     # 使用更簡單可靠的 IP 檢測服務
#     local ip=$(curl -s --socks5-hostname "${PROXY_SERVER}:${PROXY_PORT}" \
#                     -U "${NORDVPN_USER}:${NORDVPN_PASS}" \
#                     --max-time 10 \
#                     https://api.ipify.org 2>/dev/null)
    
#     if [ -z "$ip" ]; then
#         # 備用方法
#         ip=$(curl -s --socks5-hostname "${PROXY_SERVER}:${PROXY_PORT}" \
#                   -U "${NORDVPN_USER}:${NORDVPN_PASS}" \
#                   --max-time 10 \
#                   https://ifconfig.me 2>/dev/null)
#     fi
    
#     if [ -n "$ip" ]; then
#         echo -e "${GREEN}✓ 代理連接成功！${NC}"
#         echo -e "你的 IP 地址現在是: ${GREEN}$ip${NC}"
#     else
#         echo -e "${RED}✗ 代理連接失敗${NC}"
#         echo "請檢查你的憑證和網絡連接"
#         echo "調試命令: curl -v --socks5-hostname ${PROXY_SERVER}:${PROXY_PORT} -U 用戶名:密碼 https://api.ipify.org"
#     fi
# }

# # 為特定命令使用代理（一次性）
# with_proxy() {
#     if [ $# -eq 0 ]; then
#         echo "用法: with_proxy <命令>"
#         echo "示例: with_proxy curl https://api.github.com"
#         return 1
#     fi
    
#     ALL_PROXY="socks5://${NORDVPN_USER}:${NORDVPN_PASS}@${PROXY_SERVER}:${PROXY_PORT}" \
#     HTTP_PROXY="$ALL_PROXY" \
#     HTTPS_PROXY="$ALL_PROXY" \
#     NO_PROXY="localhost,127.0.0.1,*.local" \
#     "$@"
# }

# # 主函數
# case "$1" in
#     on)
#         proxy_on
#         ;;
#     off)
#         proxy_off
#         ;;
#     status)
#         proxy_status
#         ;;
#     test)
#         test_proxy
#         ;;
#     with)
#         shift
#         with_proxy "$@"
#         ;;
#     *)
#         echo "NordVPN 終端代理管理工具"
#         echo ""
#         echo "用法: $0 {on|off|status|test|with <命令>}"
#         echo ""
#         echo "命令:"
#         echo "  on      - 在當前終端啟用代理"
#         echo "  off     - 在當前終端禁用代理"
#         echo "  status  - 顯示代理狀態"
#         echo "  test    - 測試代理連接"
#         echo "  with    - 為單個命令使用代理"
#         echo ""
#         echo "示例:"
#         echo "  $0 on                    # 啟用代理"
#         echo "  $0 with curl github.com  # 只為 curl 命令使用代理"
#         exit 1
#         ;;
# esac

# # === 額外的便利函數（添加到 ~/.zshrc 或 ~/.bash_profile）===

# # Git 代理快速切換
# git_proxy_on() {
#     git config --global http.proxy "socks5://${NORDVPN_USER}:${NORDVPN_PASS}@${PROXY_SERVER}:${PROXY_PORT}"
#     git config --global https.proxy "socks5://${NORDVPN_USER}:${NORDVPN_PASS}@${PROXY_SERVER}:${PROXY_PORT}"
#     echo -e "${GREEN}Git 代理已啟用${NC}"
# }

# git_proxy_off() {
#     git config --global --unset http.proxy
#     git config --global --unset https.proxy
#     echo -e "${GREEN}Git 代理已禁用${NC}"
# }

# # npm 代理設置（需要 HTTP 代理轉換）
# npm_proxy_setup() {
#     echo -e "${YELLOW}npm 不直接支持 SOCKS5，需要使用代理轉換工具${NC}"
#     echo "建議安裝 gost 進行 SOCKS5 到 HTTP 的轉換："
#     echo "  brew install gost"
#     echo "  gost -L=http://:8080 -F=socks5://${NORDVPN_USER}:${NORDVPN_PASS}@${PROXY_SERVER}:${PROXY_PORT}"
#     echo ""
#     echo "然後設置 npm 使用本地 HTTP 代理："
#     echo "  npm config set proxy http://127.0.0.1:8080"
#     echo "  npm config set https-proxy http://127.0.0.1:8080"
# }


#!/bin/bash
# nordvpn-terminal-proxy.sh
# 只在當前終端會話中設置代理，不影響其他程序

# 配置你的 NordVPN SOCKS5 憑證
NORDVPN_USER="2qfCDkqLEw8P3kf4ZAdm5zMu"
NORDVPN_PASS="7R4LttY9hDK8rHjSmvBPM7q7"

# 選擇最近的 SOCKS5 服務器（亞洲用戶建議使用新加坡或日本附近的服務器）
# 由於沒有台灣的 SOCKS5，建議使用美西服務器以獲得較好的延遲
PROXY_SERVER="los-angeles.us.socks.nordhold.net"
PROXY_PORT="1080"

# 顏色定義
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 啟用代理函數
proxy_on() {
    # 檢查是否需要啟動 HTTP 代理轉換服務
    if [ "$1" = "--http" ] || [ "$USE_HTTP_PROXY" = "true" ]; then
        start_http_proxy_service
        
        # 使用 HTTP 代理
        export HTTP_PROXY="http://127.0.0.1:8888"
        export HTTPS_PROXY="http://127.0.0.1:8888"
        export http_proxy="$HTTP_PROXY"
        export https_proxy="$HTTPS_PROXY"
        export ALL_PROXY="$HTTP_PROXY"
        
        echo -e "${GREEN}✓ HTTP 代理已在當前終端啟用${NC}"
        echo -e "${YELLOW}HTTP 代理: 127.0.0.1:8888 (通過 ${PROXY_SERVER})${NC}"
    else
        # 僅設置 SOCKS5 代理
        export ALL_PROXY="socks5://${NORDVPN_USER}:${NORDVPN_PASS}@${PROXY_SERVER}:${PROXY_PORT}"
        export HTTP_PROXY="$ALL_PROXY"
        export HTTPS_PROXY="$ALL_PROXY"
        export http_proxy="$ALL_PROXY"
        export https_proxy="$ALL_PROXY"
        
        echo -e "${GREEN}✓ SOCKS5 代理已在當前終端啟用${NC}"
        echo -e "${YELLOW}代理服務器: ${PROXY_SERVER}:${PROXY_PORT}${NC}"
        echo -e "${YELLOW}提示: 某些工具可能需要 HTTP 代理，使用 'proxy_on --http' 啟用${NC}"
    fi
    
    export NO_PROXY="localhost,127.0.0.1,*.local,10.0.0.0/8,**********/12,***********/16"
    export no_proxy="$NO_PROXY"
    
    # 測試連接
    test_proxy
}

# 啟動 HTTP 代理轉換服務
start_http_proxy_service() {
    # 檢查 gost 是否已安裝
    if ! command -v gost &> /dev/null; then
        echo -e "${RED}需要安裝 gost 來支持 HTTP 代理${NC}"
        echo "請運行: brew install gost"
        echo "然後重新運行此命令"
        return 1
    fi
    
    # 檢查是否已有服務在運行
    if lsof -i:8888 &> /dev/null; then
        echo -e "${YELLOW}HTTP 代理服務已在運行${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}正在啟動 HTTP 代理轉換服務...${NC}"
    
    # 啟動 gost（後台運行）
    nohup gost -L=http://:8888 -F="socks5://${NORDVPN_USER}:${NORDVPN_PASS}@${PROXY_SERVER}:${PROXY_PORT}" \
        > ~/.gost-proxy.log 2>&1 &
    
    local GOST_PID=$!
    echo $GOST_PID > ~/.gost-proxy.pid
    
    # 等待服務啟動
    sleep 2
    
    if ps -p $GOST_PID > /dev/null; then
        echo -e "${GREEN}✓ HTTP 代理服務已啟動 (PID: $GOST_PID)${NC}"
        return 0
    else
        echo -e "${RED}✗ HTTP 代理服務啟動失敗${NC}"
        echo "查看日誌: cat ~/.gost-proxy.log"
        return 1
    fi
}

# 禁用代理函數
proxy_off() {
    unset ALL_PROXY HTTP_PROXY HTTPS_PROXY http_proxy https_proxy NO_PROXY no_proxy
    
    # 停止 HTTP 代理服務（如果在運行）
    if [ -f ~/.gost-proxy.pid ]; then
        local PID=$(cat ~/.gost-proxy.pid)
        if ps -p $PID > /dev/null 2>&1; then
            kill $PID
            echo -e "${GREEN}✓ HTTP 代理服務已停止${NC}"
        fi
        rm -f ~/.gost-proxy.pid
    fi
    
    echo -e "${GREEN}✓ 代理已在當前終端禁用${NC}"
}

# 檢查代理狀態
proxy_status() {
    if [ -n "$ALL_PROXY" ]; then
        echo -e "${GREEN}代理狀態: 已啟用${NC}"
        echo -e "當前代理: $ALL_PROXY"
        echo -e "排除列表: $NO_PROXY"
    else
        echo -e "${RED}代理狀態: 未啟用${NC}"
    fi
}

# 測試代理連接
test_proxy() {
    echo -e "\n${YELLOW}正在測試代理連接...${NC}"
    
    # 使用更簡單可靠的 IP 檢測服務
    local ip=$(curl -s --socks5-hostname "${PROXY_SERVER}:${PROXY_PORT}" \
                    -U "${NORDVPN_USER}:${NORDVPN_PASS}" \
                    --max-time 10 \
                    https://api.ipify.org 2>/dev/null)
    
    if [ -z "$ip" ]; then
        # 備用方法
        ip=$(curl -s --socks5-hostname "${PROXY_SERVER}:${PROXY_PORT}" \
                  -U "${NORDVPN_USER}:${NORDVPN_PASS}" \
                  --max-time 10 \
                  https://ifconfig.me 2>/dev/null)
    fi
    
    if [ -n "$ip" ]; then
        echo -e "${GREEN}✓ 代理連接成功！${NC}"
        echo -e "你的 IP 地址現在是: ${GREEN}$ip${NC}"
    else
        echo -e "${RED}✗ 代理連接失敗${NC}"
        echo "請檢查你的憑證和網絡連接"
        echo "調試命令: curl -v --socks5-hostname ${PROXY_SERVER}:${PROXY_PORT} -U 用戶名:密碼 https://api.ipify.org"
    fi
}

# 為特定命令使用代理（一次性）
with_proxy() {
    if [ $# -eq 0 ]; then
        echo "用法: with_proxy <命令>"
        echo "示例: with_proxy curl https://api.github.com"
        return 1
    fi
    
    ALL_PROXY="socks5://${NORDVPN_USER}:${NORDVPN_PASS}@${PROXY_SERVER}:${PROXY_PORT}" \
    HTTP_PROXY="$ALL_PROXY" \
    HTTPS_PROXY="$ALL_PROXY" \
    NO_PROXY="localhost,127.0.0.1,*.local" \
    "$@"
}

# 主函數
case "$1" in
    on)
        shift  # 移除 'on' 參數
        proxy_on "$@"
        ;;
    off)
        proxy_off
        ;;
    status)
        proxy_status
        ;;
    test)
        test_proxy
        ;;
    with)
        shift
        with_proxy "$@"
        ;;
    *)
        echo "NordVPN 終端代理管理工具"
        echo ""
        echo "用法: $0 {on|off|status|test|with <命令>}"
        echo ""
        echo "命令:"
        echo "  on          - 在當前終端啟用 SOCKS5 代理"
        echo "  on --http   - 在當前終端啟用 HTTP 代理（需要 gost）"
        echo "  off         - 在當前終端禁用代理"
        echo "  status      - 顯示代理狀態"
        echo "  test        - 測試代理連接"
        echo "  with        - 為單個命令使用代理"
        echo ""
        echo "示例:"
        echo "  $0 on                    # 啟用 SOCKS5 代理"
        echo "  $0 on --http             # 啟用 HTTP 代理（適用於更多工具）"
        echo "  $0 with curl github.com  # 只為 curl 命令使用代理"
        exit 1
        ;;
esac

# === 額外的便利函數（添加到 ~/.zshrc 或 ~/.bash_profile）===

# Git 代理快速切換
git_proxy_on() {
    git config --global http.proxy "socks5://${NORDVPN_USER}:${NORDVPN_PASS}@${PROXY_SERVER}:${PROXY_PORT}"
    git config --global https.proxy "socks5://${NORDVPN_USER}:${NORDVPN_PASS}@${PROXY_SERVER}:${PROXY_PORT}"
    echo -e "${GREEN}Git 代理已啟用${NC}"
}

git_proxy_off() {
    git config --global --unset http.proxy
    git config --global --unset https.proxy
    echo -e "${GREEN}Git 代理已禁用${NC}"
}

# npm 代理設置（需要 HTTP 代理轉換）
npm_proxy_setup() {
    echo -e "${YELLOW}npm 不直接支持 SOCKS5，需要使用代理轉換工具${NC}"
    echo "建議安裝 gost 進行 SOCKS5 到 HTTP 的轉換："
    echo "  brew install gost"
    echo "  gost -L=http://:8080 -F=socks5://${NORDVPN_USER}:${NORDVPN_PASS}@${PROXY_SERVER}:${PROXY_PORT}"
    echo ""
    echo "然後設置 npm 使用本地 HTTP 代理："
    echo "  npm config set proxy http://127.0.0.1:8080"
    echo "  npm config set https-proxy http://127.0.0.1:8080"
}